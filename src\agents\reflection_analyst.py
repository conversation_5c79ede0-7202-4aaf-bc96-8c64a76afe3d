import json
import os
from datetime import datetime, timedelta
from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field
from typing_extensions import Literal
from src.graph.state import AgentState, show_agent_reasoning
from src.utils.progress import progress
from src.utils.llm import call_llm


class ReflectionAnalysis(BaseModel):
    decision_quality: Literal["excellent", "good", "fair", "poor"]
    correctness_score: float = Field(description="Score from 0-100 indicating decision correctness")
    key_insights: list[str] = Field(description="Key insights from analyzing the decision")
    recommendations: list[str] = Field(description="Specific recommendations for future decisions")
    reasoning: str = Field(description="Detailed reasoning for the analysis")


class ReflectionOutput(BaseModel):
    reflections: dict[str, ReflectionAnalysis] = Field(description="Dictionary of ticker to reflection analysis")


def reflection_analyst_agent(state: AgentState):
    """
    Reflection Analyst Agent - Analyzes portfolio manager decisions and provides reflection recommendations
    Runs after daily trading ends to provide improvement suggestions for the next day
    """
    data = state["data"]
    tickers = data["tickers"]
    end_date = data["end_date"]

    # Get current portfolio decisions (parse from the last message)
    portfolio_decisions = {}
    for message in reversed(state["messages"]):
        if message.name == "portfolio_manager":
            try:
                portfolio_decisions = json.loads(message.content)
                break
            except json.JSONDecodeError:
                continue

    if not portfolio_decisions:
        progress.update_status("reflection_analyst", None, "No portfolio decisions found")
        return {"messages": state["messages"], "data": data}

    # Get all analyst signals
    analyst_signals = data.get("analyst_signals", {})

    # Generate reflection analysis
    reflection_analysis = {}

    for ticker in tickers:
        if ticker in portfolio_decisions:
            progress.update_status("reflection_analyst", ticker, "Analyzing decision quality")

            # Get the decision and related signals for this ticker
            decision = portfolio_decisions[ticker]
            ticker_signals = {}

            # Collect signals from all agents for this ticker
            for agent_name, signals in analyst_signals.items():
                if agent_name != "risk_management_agent" and ticker in signals:
                    ticker_signals[agent_name] = signals[ticker]

            # Generate reflection analysis
            reflection = generate_reflection_analysis(
                ticker=ticker,
                decision=decision,
                analyst_signals=ticker_signals,
                model_name=state["metadata"]["model_name"],
                model_provider=state["metadata"]["model_provider"],
            )

            reflection_analysis[ticker] = {
                "decision_quality": reflection.decision_quality,
                "correctness_score": reflection.correctness_score,
                "key_insights": reflection.key_insights,
                "recommendations": reflection.recommendations,
                "reasoning": reflection.reasoning,
            }

            progress.update_status("reflection_analyst", ticker, "Done")

    # Save reflection analysis to file (for next day reference)
    save_reflection_to_file(reflection_analysis, end_date, tickers)

    # Create message
    message = HumanMessage(
        content=json.dumps(reflection_analysis),
        name="reflection_analyst",
    )

    # Show reasoning process
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(reflection_analysis, "Reflection Analyst")

    # Add reflection analysis to state
    state["data"]["analyst_signals"]["reflection_analyst"] = reflection_analysis

    progress.update_status("reflection_analyst", None, "Done")

    return {
        "messages": state["messages"] + [message],
        "data": data,
    }


def generate_reflection_analysis(
    ticker: str,
    decision: dict,
    analyst_signals: dict,
    model_name: str,
    model_provider: str,
) -> ReflectionAnalysis:
    """Generate reflection analysis using LLM"""

    # 增强输入数据验证和日志记录
    try:
        # 验证输入数据
        if not decision:
            progress.update_status("reflection_analyst", ticker, "Warning: Empty decision data")

        if not analyst_signals:
            progress.update_status("reflection_analyst", ticker, "Warning: Empty analyst signals")

        # 记录输入数据大小用于调试
        decision_str = json.dumps(decision, indent=2, ensure_ascii=False)
        signals_str = json.dumps(analyst_signals, indent=2, ensure_ascii=False)

        progress.update_status("reflection_analyst", ticker,
                             f"Processing decision ({len(decision_str)} chars) and signals ({len(signals_str)} chars)")

    except Exception as validation_error:
        progress.update_status("reflection_analyst", ticker, f"Input validation error: {validation_error}")

    template = ChatPromptTemplate.from_messages([
        (
            "system",
            """You are a professional investment reflection analyst responsible for analyzing the decision quality of portfolio managers.

Your tasks are:
1. Analyze whether investment decisions are reasonable and fully consider signals from all analysts
2. Evaluate the logical consistency and risk management of decisions
3. Identify strengths and potential issues in decisions
4. Provide specific improvement recommendations

Evaluation criteria:
- excellent: Decision is completely reasonable, fully considers all signals, and has proper risk control
- good: Decision is basically reasonable, considers most signals, with slight room for improvement
- fair: Decision has some reasonableness but has obvious deficiencies or insufficient signal utilization
- poor: Decision has major problems, improper signal analysis, or insufficient risk control

Please provide detailed analysis and specific improvement recommendations.""",
        ),
        (
            "human",
            """Please analyze the quality of the following investment decision:

Ticker: {ticker}

Portfolio Manager's Decision:
{decision}

Analyst Signals:
{analyst_signals}

Please return analysis in the following JSON format:
{{
  "decision_quality": "excellent" | "good" | "fair" | "poor",
  "correctness_score": score between 0-100,
  "key_insights": ["key insight 1", "key insight 2", ...],
  "recommendations": ["recommendation 1", "recommendation 2", ...],
  "reasoning": "detailed analysis reasoning process"
}}""",
        ),
    ])

    try:
        prompt = template.invoke({
            "ticker": ticker,
            "decision": json.dumps(decision, indent=2, ensure_ascii=False),
            "analyst_signals": json.dumps(analyst_signals, indent=2, ensure_ascii=False),
        })
    except Exception as prompt_error:
        progress.update_status("reflection_analyst", ticker, f"Prompt creation error: {prompt_error}")
        return create_default_reflection_with_error(f"Prompt creation failed: {prompt_error}")

    def create_default_reflection():
        return ReflectionAnalysis(
            decision_quality="fair",
            correctness_score=50.0,
            key_insights=["Error occurred during analysis process"],
            recommendations=["Need to re-evaluate decision logic"],
            reasoning="Error occurred during reflection analysis process, defaulting to fair evaluation"
        )


    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=ReflectionAnalysis,
        agent_name="reflection_analyst",
        default_factory=create_default_reflection,
    )


def create_default_reflection_with_error(error_message: str) -> ReflectionAnalysis:
    """Create default reflection with specific error message"""
    return ReflectionAnalysis(
        decision_quality="fair",
        correctness_score=50.0,
        key_insights=[f"Error occurred during analysis process: {error_message}"],
        recommendations=["Need to re-evaluate decision logic", "Check input data format"],
        reasoning=f"Error occurred during reflection analysis process: {error_message}, defaulting to fair evaluation"
    )


def save_reflection_to_file(reflection_analysis: dict, end_date: str, tickers: list[str]):
    """Save reflection analysis to file for next day use"""
    try:
        # Create reflection log directory
        reflection_dir = "reasoning_logs/reflections"
        os.makedirs(reflection_dir, exist_ok=True)

        # Use date as filename
        filename = f"{reflection_dir}/reflection_{end_date}.json"

        # Save data
        reflection_data = {
            "date": end_date,
            "tickers": tickers,
            "reflections": reflection_analysis,
            "timestamp": datetime.now().isoformat()
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(reflection_data, f, indent=2, ensure_ascii=False)

        progress.update_status("reflection_analyst", None, f"Reflection saved to {filename}")

    except Exception as e:
        progress.update_status("reflection_analyst", None, f"Failed to save reflection: {str(e)}")


def load_previous_reflection(current_date: str, tickers: list[str]) -> dict:
    """Load previous day's reflection analysis"""
    try:
        # Calculate previous trading day (simplified handling, should consider weekends and holidays in practice)
        current_dt = datetime.strptime(current_date, "%Y-%m-%d")
        previous_dt = current_dt - timedelta(days=1)
        previous_date = previous_dt.strftime("%Y-%m-%d")

        reflection_dir = "reasoning_logs/reflections"
        filename = f"{reflection_dir}/reflection_{previous_date}.json"

        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as f:
                reflection_data = json.load(f)
                return reflection_data.get("reflections", {})

        return {}

    except Exception as e:
        progress.update_status("reflection_analyst", None, f"Failed to load previous reflection: {str(e)}")
        return {}
