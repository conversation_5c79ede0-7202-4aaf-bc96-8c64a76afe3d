#!/usr/bin/env python3
"""
反思代理测试脚本
用于诊断和测试反思代理在处理NVDA数据时的问题
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.reflection_analyst import generate_reflection_analysis, ReflectionAnalysis
from src.utils.progress import progress


def load_test_data() -> Dict[str, Any]:
    """加载测试数据"""
    # 模拟投资组合决策数据
    test_decision = {
        "action": "hold",
        "quantity": 0,
        "confidence": 65.0,
        "reasoning": "Mixed signals from analysts, maintaining current position"
    }
    
    # 模拟分析师信号数据
    test_analyst_signals = {
        "fundamentals_analyst": {
            "signal": "bullish",
            "confidence": 80.0,
            "reasoning": "Strong financial metrics and growth prospects"
        },
        "market_analyst": {
            "signal": "bearish", 
            "confidence": 70.0,
            "reasoning": "Technical indicators suggest potential downside"
        },
        "news_analyst": {
            "signal": "neutral",
            "confidence": 60.0,
            "reasoning": "Mixed news sentiment with no clear direction"
        }
    }
    
    return test_decision, test_analyst_signals


def test_reflection_with_different_models():
    """测试不同模型的反思代理功能"""
    test_decision, test_analyst_signals = load_test_data()
    
    # 测试不同的模型配置
    test_configs = [
        {"model_name": "deepseek-chat", "model_provider": "deepseek"},
        {"model_name": "gpt-3.5-turbo", "model_provider": "openai"},
        {"model_name": "gemini-2.0-flash-exp", "model_provider": "qingyun"},
        {"model_name": "meta-llama/llama-4-scout:free", "model_provider": "openrouter"},
    ]
    
    results = {}
    
    for config in test_configs:
        model_name = config["model_name"]
        model_provider = config["model_provider"]
        
        print(f"\n=== 测试模型: {model_name} ({model_provider}) ===")
        
        try:
            # 测试反思分析
            result = generate_reflection_analysis(
                ticker="NVDA",
                decision=test_decision,
                analyst_signals=test_analyst_signals,
                model_name=model_name,
                model_provider=model_provider
            )
            
            results[f"{model_name}_{model_provider}"] = {
                "success": True,
                "result": {
                    "decision_quality": result.decision_quality,
                    "correctness_score": result.correctness_score,
                    "key_insights": result.key_insights,
                    "recommendations": result.recommendations,
                    "reasoning": result.reasoning[:200] + "..." if len(result.reasoning) > 200 else result.reasoning
                }
            }
            
            print(f"✅ 成功 - 决策质量: {result.decision_quality}, 分数: {result.correctness_score}")
            
        except Exception as e:
            results[f"{model_name}_{model_provider}"] = {
                "success": False,
                "error": str(e)
            }
            print(f"❌ 失败 - 错误: {str(e)[:100]}...")
    
    return results


def test_with_real_nvda_data():
    """使用真实的NVDA数据进行测试"""
    print("\n=== 使用真实NVDA数据测试 ===")
    
    # 尝试加载真实的NVDA决策数据
    try:
        # 查找最近的NVDA投资组合决策
        experiment_dirs = [
            "reasoning_logs/experiment_NVDA_20250101-20250601_deepseekv3",
            "reasoning_logs/experiment_NVDA_20250101-20250601_gpt3.5",
            "reasoning_logs/experiment_NVDA_20250101-20250601_gemini2.0"
        ]
        
        real_data = None
        for exp_dir in experiment_dirs:
            if os.path.exists(exp_dir):
                # 查找2025-01-01的数据
                date_dir = os.path.join(exp_dir, "2025-01-01")
                if os.path.exists(date_dir):
                    # 查找投资组合管理器的决策
                    for file in os.listdir(date_dir):
                        if "portfolio_manager" in file and file.endswith(".json"):
                            with open(os.path.join(date_dir, file), 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                real_data = data.get("reasoning", {})
                                break
                    if real_data:
                        break
        
        if real_data:
            print("✅ 找到真实NVDA决策数据")
            
            # 使用DeepSeek模型测试
            result = generate_reflection_analysis(
                ticker="NVDA",
                decision=real_data,
                analyst_signals={},  # 简化测试
                model_name="deepseek-chat",
                model_provider="deepseek"
            )
            
            print(f"决策质量: {result.decision_quality}")
            print(f"正确性分数: {result.correctness_score}")
            print(f"关键洞察: {result.key_insights}")
            
            return True
        else:
            print("❌ 未找到真实NVDA决策数据")
            return False
            
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")
        return False


def save_test_results(results: Dict[str, Any]):
    """保存测试结果"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"reasoning_logs/reflection_agent_test_{timestamp}.json"
    
    os.makedirs("reasoning_logs", exist_ok=True)
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n测试结果已保存到: {results_file}")


def main():
    """主测试函数"""
    print("🔍 反思代理诊断测试开始")
    print("=" * 50)
    
    # 测试不同模型
    model_results = test_reflection_with_different_models()
    
    # 测试真实数据
    real_data_success = test_with_real_nvda_data()
    
    # 汇总结果
    all_results = {
        "timestamp": datetime.now().isoformat(),
        "model_tests": model_results,
        "real_data_test": real_data_success,
        "summary": {
            "total_models_tested": len(model_results),
            "successful_models": len([r for r in model_results.values() if r["success"]]),
            "failed_models": len([r for r in model_results.values() if not r["success"]]),
            "real_data_test_passed": real_data_success
        }
    }
    
    # 保存结果
    save_test_results(all_results)
    
    # 打印总结
    print("\n" + "=" * 50)
    print("🎯 测试总结:")
    print(f"总共测试模型数: {all_results['summary']['total_models_tested']}")
    print(f"成功的模型数: {all_results['summary']['successful_models']}")
    print(f"失败的模型数: {all_results['summary']['failed_models']}")
    print(f"真实数据测试: {'✅ 通过' if real_data_success else '❌ 失败'}")
    
    # 提供建议
    if all_results['summary']['failed_models'] > 0:
        print("\n💡 建议:")
        print("1. 检查失败模型的API密钥配置")
        print("2. 验证网络连接和API服务状态")
        print("3. 查看详细错误日志文件")
        print("4. 考虑使用成功的模型作为备选方案")


if __name__ == "__main__":
    main()
