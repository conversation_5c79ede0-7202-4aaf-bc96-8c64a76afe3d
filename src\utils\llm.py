"""Helper functions for LLM"""

import json
import logging
import time
from typing import TypeVar, Type, Optional, Any
from pydantic import BaseModel
from src.llm.models import get_model, get_model_info, ModelProvider
from src.utils.progress import progress

# Import deterministic utilities
try:
    from src.utils.deterministic import (
        is_deterministic_mode,
        get_deterministic_seed,
        create_deterministic_hash,
        validate_deterministic_response
    )
except ImportError:
    # Fallback if deterministic module is not available
    def is_deterministic_mode(): return False
    def get_deterministic_seed(): return 42
    def create_deterministic_hash(data, context=""): return ""
    def validate_deterministic_response(response, keys): return True

logger = logging.getLogger(__name__)

T = TypeVar("T", bound=BaseModel)


def call_llm(
    prompt: Any,
    model_name: str,
    model_provider: str,
    pydantic_model: Type[T],
    agent_name: Optional[str] = None,
    max_retries: int = 3,
    default_factory=None,
) -> T:
    """
    Makes an LLM call with retry logic, handling both JSON supported and non-JSON supported models.
    Enhanced with deterministic behavior support.

    Args:
        prompt: The prompt to send to the LLM
        model_name: Name of the model to use
        model_provider: Provider of the model
        pydantic_model: The Pydantic model class to structure the output
        agent_name: Optional name of the agent for progress updates
        max_retries: Maximum number of retries (default: 3)
        default_factory: Optional factory function to create default response on failure

    Returns:
        An instance of the specified Pydantic model
    """

    model_info = get_model_info(model_name, model_provider)
    llm = get_model(model_name, ModelProvider(model_provider))

    # Log deterministic mode status
    if is_deterministic_mode():
        logger.debug(f"Deterministic LLM call for {agent_name or 'unknown'} with seed {get_deterministic_seed()}")
        # In deterministic mode, use fewer retries to avoid masking issues
        max_retries = min(max_retries, 2)

    # Only use structured output for models that support JSON mode
    if model_info and model_info.has_json_mode():
        llm = llm.with_structured_output(
            pydantic_model,
            method="json_mode",
        )

    # Call the LLM with retries
    for attempt in range(max_retries):
        try:
            # Call the LLM
            result = llm.invoke(prompt)

            # For non-JSON support models, we need to extract and parse the JSON manually
            if model_info and not model_info.has_json_mode():
                # Extract content from the result
                content = ""
                if hasattr(result, 'content'):
                    content = str(getattr(result, 'content', ''))
                else:
                    content = str(result)

                parsed_result = extract_json_from_response(content)
                if parsed_result:
                    try:
                        return pydantic_model(**parsed_result)
                    except Exception as e:
                        raise ValueError(f"Failed to create Pydantic model from parsed JSON: {e}")
                else:
                    # If JSON extraction failed, raise an exception to trigger retry
                    raise ValueError(f"Failed to extract valid JSON from response: {content[:200]}...")
            else:
                # For models with JSON mode, result should already be the correct type
                if isinstance(result, pydantic_model):
                    return result
                else:
                    # Try to convert if needed
                    try:
                        return pydantic_model(**result) if isinstance(result, dict) else result
                    except Exception:
                        return result

        except Exception as e:
            if agent_name:
                progress.update_status(agent_name, None, f"Error - retry {attempt + 1}/{max_retries}")

            error_str = str(e).lower()

            # Special handling for GROQ rate limit errors
            if model_provider.lower() == "groq" and ("429" in str(e) or "rate limit" in error_str):
                from src.utils.groq_api_manager import handle_groq_rate_limit, is_groq_rate_limit_error

                if is_groq_rate_limit_error(str(e)):
                    print(f"检测到GROQ速率限制错误: {str(e)[:100]}...")

                    # 尝试切换API密钥
                    if handle_groq_rate_limit(str(e)):
                        print("已切换到新的GROQ API密钥，重新创建LLM实例...")

                        # 重新创建LLM实例使用新的API密钥
                        try:
                            llm = get_model(model_name, ModelProvider(model_provider))

                            # 重新应用structured output配置
                            if model_info and model_info.has_json_mode():
                                llm = llm.with_structured_output(
                                    pydantic_model,
                                    method="json_mode",
                                )

                            print("LLM实例重新创建成功，继续重试...")
                            continue  # 继续重试而不等待

                        except Exception as recreate_error:
                            print(f"重新创建LLM实例失败: {recreate_error}")
                    else:
                        print("无法切换到新的API密钥，所有GROQ密钥可能都已达到限制")

            # Special handling for SSL/Connection errors (especially for GROQ)
            elif any(ssl_error in error_str for ssl_error in [
                "ssl", "unexpected_eof_while_reading", "eof occurred in violation of protocol",
                "connecterror", "connection", "timeout", "read timeout"
            ]):
                if attempt < max_retries - 1:
                    # 对于SSL/连接错误，使用指数退避策略
                    wait_time = min(2 ** attempt, 30)  # 2s, 4s, 8s, 16s, max 30s
                    print(f"检测到连接错误 (SSL/网络): {str(e)[:100]}...")
                    print(f"等待 {wait_time} 秒后重试 ({attempt + 2}/{max_retries})...")
                    time.sleep(wait_time)

                    # 对于GROQ的SSL错误，尝试切换API密钥
                    if model_provider.lower() == "groq":
                        from src.utils.groq_api_manager import handle_groq_rate_limit
                        print("尝试切换GROQ API密钥以解决连接问题...")
                        if handle_groq_rate_limit("Connection error - switching key"):
                            try:
                                llm = get_model(model_name, ModelProvider(model_provider))
                                if model_info and model_info.has_json_mode():
                                    llm = llm.with_structured_output(pydantic_model, method="json_mode")
                                print("已切换API密钥并重新创建LLM实例")
                            except Exception as recreate_error:
                                print(f"重新创建LLM实例失败: {recreate_error}")

                    continue

            # General rate limit handling for other providers
            elif "429" in str(e) or "rate limit" in error_str:
                if attempt < max_retries - 1:
                    # Wait longer for rate limit errors
                    wait_time = min(60 * (attempt + 1), 300)  # 60s, 120s, 180s, max 300s
                    print(f"Rate limit hit, waiting {wait_time} seconds before retry {attempt + 2}/{max_retries}...")
                    time.sleep(wait_time)
                    continue

            if attempt == max_retries - 1:
                error_details = {
                    "error_type": type(e).__name__,
                    "error_message": str(e)[:500],  # 限制错误消息长度
                    "model_name": model_name,
                    "model_provider": model_provider,
                    "agent_name": agent_name,
                    "attempt_count": max_retries
                }

                print(f"Error in LLM call after {max_retries} attempts: {e}")
                print(f"Error details: {error_details}")

                # 记录详细错误到日志文件
                if agent_name:
                    try:
                        import os
                        from datetime import datetime

                        error_log_dir = "reasoning_logs/errors"
                        os.makedirs(error_log_dir, exist_ok=True)

                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        error_log_file = f"{error_log_dir}/{agent_name}_error_{timestamp}.json"

                        with open(error_log_file, 'w', encoding='utf-8') as f:
                            import json
                            json.dump(error_details, f, indent=2, ensure_ascii=False)

                        print(f"Error details saved to: {error_log_file}")
                    except Exception as log_error:
                        print(f"Failed to save error log: {log_error}")

                # Use default_factory if provided, otherwise create a basic default
                if default_factory:
                    return default_factory()
                return create_default_response(pydantic_model)

    # This should never be reached due to the retry logic above
    return create_default_response(pydantic_model)


def create_default_response(model_class: Type[T]) -> T:
    """Creates a safe default response based on the model's fields."""
    default_values = {}
    for field_name, field in model_class.model_fields.items():
        if field.annotation == str:
            default_values[field_name] = "Error in analysis, using default"
        elif field.annotation == float:
            default_values[field_name] = 0.0
        elif field.annotation == int:
            default_values[field_name] = 0
        elif hasattr(field.annotation, "__origin__") and getattr(field.annotation, "__origin__", None) == dict:
            default_values[field_name] = {}
        else:
            # For other types (like Literal), try to use the first allowed value
            if hasattr(field.annotation, "__args__"):
                args = getattr(field.annotation, "__args__", None)
                if args:
                    default_values[field_name] = args[0]
                else:
                    default_values[field_name] = None
            else:
                default_values[field_name] = None

    return model_class(**default_values)


def extract_json_from_response(content: str) -> Optional[dict]:
    """Extracts JSON from markdown-formatted response with enhanced parsing."""
    try:
        # Clean the content first
        content = content.strip()

        # Method 1: Try to find JSON in markdown code blocks
        json_patterns = ["```json", "```JSON", "```"]
        for pattern in json_patterns:
            json_start = content.find(pattern)
            if json_start != -1:
                json_text = content[json_start + len(pattern):]
                json_end = json_text.find("```")
                if json_end != -1:
                    json_text = json_text[:json_end].strip()
                    try:
                        return json.loads(json_text)
                    except json.JSONDecodeError:
                        continue

        # Method 2: Try to parse the entire content as JSON
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            pass

        # Method 3: Look for { and } to extract JSON with better brace matching
        start_brace = content.find("{")
        if start_brace != -1:
            # Find the matching closing brace
            brace_count = 0
            end_brace = start_brace
            for i, char in enumerate(content[start_brace:], start_brace):
                if char == "{":
                    brace_count += 1
                elif char == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        end_brace = i
                        break

            if brace_count == 0:  # Found matching braces
                json_text = content[start_brace:end_brace + 1]
                try:
                    return json.loads(json_text)
                except json.JSONDecodeError:
                    pass

        # Method 4: Try to extract JSON-like content using regex
        import re
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, content, re.DOTALL)
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue

        # Method 5: Try to construct JSON from key-value patterns
        # Look for patterns like "signal": "bullish", "confidence": 75, etc.
        signal_match = re.search(r'"signal"\s*:\s*"([^"]+)"', content, re.IGNORECASE)
        confidence_match = re.search(r'"confidence"\s*:\s*(\d+(?:\.\d+)?)', content, re.IGNORECASE)
        reasoning_match = re.search(r'"reasoning"\s*:\s*"([^"]+)"', content, re.IGNORECASE)

        if signal_match and confidence_match and reasoning_match:
            return {
                "signal": signal_match.group(1),
                "confidence": float(confidence_match.group(1)),
                "reasoning": reasoning_match.group(1)
            }

    except Exception as e:
        print(f"Error extracting JSON from response: {e}")
    return None
